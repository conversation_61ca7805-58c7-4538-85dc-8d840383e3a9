// import React from 'react';
// import {Text, View, StyleSheet} from 'react-native';

// const styles =  StyleSheet.create({
//     catContainer: {
//     backgroundColor: 'purple',
//     padding: 50,
//     margin: 5,
//     borderRadius: 8,
//   },
//    text:{
//     fontSize:40,
//     color: 'White',
//   },
// });
// export default function App() {
//   const Cat = () => {
//     return (
//       <View>
//         <Text style = {styles.text}>I am a Lion!</Text>
//       </View>
//     );
//   };

//   const Cafe = () => {
//     return (
//       <View style = {styles.catContainer}>
//         <Text style ={styles.text}>Welcome!</Text>
//         {/* Cat();
//         <Cat />
//         <Cat /> */}   
//       </View>
//     );
//   }; 

//   return (
//     <View style = {styles.catContainer}>
//       <Cafe />
//     </View>
//   );
// }
// App.tsx
import React from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createNativeStackNavigator } from '@react-navigation/native-stack';

import LoginForm from './screens/LoginScreen';
import HomeScreen from './screens/HomeScreen';


export type RootStackParamList = {
  Login: undefined;
  Home: undefined;
};

const Stack = createNativeStackNavigator<RootStackParamList>();

export default function App() {
  return (
    <NavigationContainer>
      <Stack.Navigator initialRouteName="Login">
        <Stack.Screen name="Login" component={LoginForm} />
        <Stack.Screen name="Home" component={HomeScreen} />
      </Stack.Navigator>
    </NavigationContainer>
  );
}
